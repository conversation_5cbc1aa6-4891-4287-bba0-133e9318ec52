
import React, { useState } from 'react';
import { ProviderDashboardLayout } from '@/components/provider/ProviderDashboardLayout';
import { SubscriptionPlanCard } from '@/components/provider/subscription/SubscriptionPlanCard';
import { BusinessCertificationDialog } from '@/components/provider/BusinessCertificationDialog';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/features/auth/hooks/useAuth';

const ProviderPlans = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  const [billingInterval, setBillingInterval] = useState<"monthly" | "yearly">("yearly");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<string>('');
  
  // Get current plan from user data or default to 'free'
  // In a real app, this would come from user.plan or a separate API call
  const [currentPlan, setCurrentPlan] = useState(user?.plan || 'free');
  
  // Discount calculation for yearly billing
  const getDiscountedPrice = (monthlyPrice: number) => {
    const discountedPrice = (monthlyPrice * 0.8).toFixed(0);
    return `$${discountedPrice}`;
  };
  
  // Show dialog to confirm business certification upload
  const handleCheckout = (planId: string) => {
    setSelectedPlan(planId);
    setIsDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setSelectedPlan('');
  };
  
  return (
    <ProviderDashboardLayout pageTitle="Plans & Pricing">
      <div className="space-y-6">
        {/* Subscription plans */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <SubscriptionPlanCard
            title="Starter"
            price="Free"
            description="Get started with the basic tools you need"
            commission="20%"
            isCurrentPlan={currentPlan === 'free'}
            features={[
              { included: true, text: "Access to bidding jobs" },
              { included: true, text: "Basic job alerts" },
              { included: true, text: "Standard visibility" },
              { included: true, text: "Basic dashboard analytics" },
              { included: true, text: "Community support" },
              { included: false, text: "Priority access to leads" },
              { included: false, text: "Advanced insights dashboard" },
              { included: false, text: "BNPL (Buy Now Pay Later)" },
            ]}
            ctaText="Current Plan"
            onSelect={() => {}}
            disabled={currentPlan === 'free'}
          />
          
          <SubscriptionPlanCard
            title="Pro"
            price={billingInterval === "yearly" ? getDiscountedPrice(199) : "$199"}
            description="Grow your business with more leads & tools"
            commission="15%"
            isCurrentPlan={currentPlan === 'pro'}
            isPopular={true}
            features={[
              { included: true, text: "Access to bidding jobs" },
              { included: true, text: "Priority access to leads" },
              { included: true, text: "Instant alerts" },
              { included: true, text: "Boosted visibility" },
              { included: true, text: "Advanced insights dashboard" },
              { included: true, text: "Add-on for BNPL" },
              { included: true, text: "Email support" },
              { included: false, text: "Premium featured placement" },
              { included: false, text: "Full business tools" },
            ]}
            ctaText={currentPlan === 'free' ? 'Upgrade to Pro' : currentPlan === 'pro' ? 'Current Plan' : 'Downgrade to Pro'}
            onSelect={() => handleCheckout('pro')}
            disabled={currentPlan === 'pro'}
          />
          
          <SubscriptionPlanCard
            title="Elite"
            price={billingInterval === "yearly" ? getDiscountedPrice(399) : "$399"}
            description="Maximize your business with premium tools"
            commission="12%"
            isCurrentPlan={currentPlan === 'elite'}
            features={[
              { included: true, text: "Access to bidding jobs" },
              { included: true, text: "Top placement for leads" },
              { included: true, text: "Real-time priority alerts" },
              { included: true, text: "Premium featured placement" },
              { included: true, text: "Full business tools" },
              { included: true, text: "CRM and client management" },
              { included: true, text: "BNPL included" },
              { included: true, text: "Priority support" },
            ]}
            ctaText={currentPlan === 'elite' ? 'Current Plan' : 'Upgrade to Elite'}
            onSelect={() => handleCheckout('elite')}
            disabled={currentPlan === 'elite'}
            highlighted={true}
          />
        </div>

      </div>
      
      {/* Business Certification Dialog */}
      <BusinessCertificationDialog
        isOpen={isDialogOpen}
        onClose={handleCloseDialog}
        planName={selectedPlan}
      />
    </ProviderDashboardLayout>
  );
};

export default ProviderPlans;
