import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { createBid } from '@/services/bidService';
import { CreateBidRequest } from '@/types/bid';
import { Job } from '@/types/jobs';

interface CreateBidFormProps {
  job: Job;
  onBidCreated?: () => void;
  onCancel?: () => void;
}

export const CreateBidForm: React.FC<CreateBidFormProps> = ({
  job,
  onBidCreated,
  onCancel
}) => {
  const [formData, setFormData] = useState<CreateBidRequest>({
    jobId: job.id,
    amount: 0,
    description: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const handleInputChange = (field: keyof CreateBidRequest, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (formData.amount <= 0) {
      toast({
        title: 'Invalid Amount',
        description: 'Please enter a valid bid amount greater than 0.',
        variant: 'destructive'
      });
      return;
    }

    if (!formData.description.trim()) {
      toast({
        title: 'Description Required',
        description: 'Please provide a description for your bid.',
        variant: 'destructive'
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await createBid(formData);
      
      if (response.isSuccess) {
        toast({
          title: 'Bid Submitted',
          description: 'Your bid has been successfully submitted.',
        });
        onBidCreated?.();
      } else {
        toast({
          title: 'Submission Failed',
          description: response.error || 'Failed to submit bid. Please try again.',
          variant: 'destructive'
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Submit Your Bid</CardTitle>
        <div className="text-sm text-gray-600">
          <p><strong>Job:</strong> {job.title}</p>
          <p><strong>Service Type:</strong> {job.serviceType}</p>
          {job.location && <p><strong>Location:</strong> {job.location}</p>}
        </div>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="amount">Bid Amount ($)</Label>
            <Input
              id="amount"
              type="number"
              min="0"
              step="0.01"
              value={formData.amount || ''}
              onChange={(e) => handleInputChange('amount', parseFloat(e.target.value) || 0)}
              placeholder="Enter your bid amount"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Describe your approach, timeline, and any additional details..."
              rows={4}
              required
            />
          </div>

          <div className="flex gap-3 justify-end">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            )}
            <Button
              type="submit"
              disabled={isSubmitting}
              className="min-w-[120px]"
            >
              {isSubmitting ? 'Submitting...' : 'Submit Bid'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};