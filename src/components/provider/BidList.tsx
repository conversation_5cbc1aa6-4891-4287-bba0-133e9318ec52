import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { getProviderBids, withdrawBid } from '@/services/bidService';
import { Bid, BidFilters, BidSortOptions, BidStatus } from '@/types/bid';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { Loader2, Eye, X } from 'lucide-react';

interface BidListProps {
  providerId?: string;
  onBidSelect?: (bid: Bid) => void;
}

export const BidList: React.FC<BidListProps> = ({ providerId, onBidSelect }) => {
  const [bids, setBids] = useState<Bid[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<BidFilters>({});
  const [sort, setSort] = useState<BidSortOptions>({ field: 'submittedAt', direction: 'desc' });
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const { toast } = useToast();
  const { user: currentUser } = useAuth();
  const currentProviderId = providerId || currentUser?.id;

  const fetchBids = useCallback(async () => {
    if (!currentProviderId) return;
    
    setLoading(true);
    try {
      const response = await getProviderBids(currentProviderId, filters, sort, page, 10);
      
      if (response.isSuccess && response.data) {
        setBids(response.data.bids);
        setTotalPages(response.data.totalPages);
      } else {
        toast({
          title: 'Error',
          description: response.error || 'Failed to fetch bids',
          variant: 'destructive'
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  }, [currentProviderId, filters, sort, page, toast]);

  useEffect(() => {
    fetchBids();
  }, [fetchBids]);

  const handleWithdrawBid = async (bidId: string) => {
    try {
      const response = await withdrawBid(bidId);
      
      if (response.isSuccess) {
        toast({
          title: 'Bid Withdrawn',
          description: 'Your bid has been successfully withdrawn.',
        });
        fetchBids(); // Refresh the list
      } else {
        toast({
          title: 'Error',
          description: response.error || 'Failed to withdraw bid',
          variant: 'destructive'
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive'
      });
    }
  };

  const handleFilterChange = (key: keyof BidFilters, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value || undefined
    }));
    setPage(1);
  };

  const handleSortChange = (field: string) => {
    setSort(prev => ({
      field: field as BidSortOptions['field'],
      direction: prev.field === field && prev.direction === 'desc' ? 'asc' : 'desc'
    }));
    setPage(1);
  };

  const getBidStatusColor = (status: BidStatus): string => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'accepted': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'withdrawn': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading && bids.length === 0) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filter Bids</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Input
                placeholder="Search by job title..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div>
              <Select onValueChange={(value) => handleFilterChange('status', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Statuses</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="accepted">Accepted</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                  <SelectItem value="withdrawn">Withdrawn</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Select onValueChange={(value) => handleSortChange(value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="submittedAt">Date Submitted</SelectItem>
                  <SelectItem value="amount">Bid Amount</SelectItem>
                  <SelectItem value="updatedAt">Last Updated</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Bid List */}
      <div className="space-y-4">
        {bids.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <p className="text-gray-500">No bids found</p>
            </CardContent>
          </Card>
        ) : (
          bids.map((bid) => (
            <Card key={bid.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex-1">
                    <h3 className="font-semibold text-lg mb-2">
                      {bid.job?.title || 'Job Title'}
                    </h3>
                    <p className="text-gray-600 mb-2">
                      {bid.job?.serviceType} • {bid.job?.location}
                    </p>
                    <p className="text-sm text-gray-500 mb-3">
                      {bid.description}
                    </p>
                  </div>
                  <div className="text-right ml-4">
                    <div className="text-2xl font-bold text-green-600 mb-2">
                      ${bid.amount.toFixed(2)}
                    </div>
                    <Badge className={getBidStatusColor(bid.status)}>
                      {bid.status.charAt(0).toUpperCase() + bid.status.slice(1)}
                    </Badge>
                  </div>
                </div>
                
                <div className="flex justify-between items-center text-sm text-gray-500">
                  <span>Submitted: {formatDate(bid.submittedAt)}</span>
                  <div className="flex gap-2">
                    {onBidSelect && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onBidSelect(bid)}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                    )}
                    {bid.status === 'pending' && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleWithdrawBid(bid.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <X className="h-4 w-4 mr-1" />
                        Withdraw
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center gap-2">
          <Button
            variant="outline"
            onClick={() => setPage(p => Math.max(1, p - 1))}
            disabled={page === 1}
          >
            Previous
          </Button>
          <span className="flex items-center px-4">
            Page {page} of {totalPages}
          </span>
          <Button
            variant="outline"
            onClick={() => setPage(p => Math.min(totalPages, p + 1))}
            disabled={page === totalPages}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  );
};