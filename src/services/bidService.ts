import { apiService, ApiResponse } from './api';
import {
  Bid,
  CreateBidRequest,
  UpdateBidRequest,
  BidFilters,
  BidSortOptions,
  BidListResponse,
  BidStatistics,
  BidActionResponse
} from '../types/bid';

/**
 * Create a new bid
 */
export const createBid = async (bidData: CreateBidRequest): Promise<ApiResponse<Bid>> => {
  return apiService<Bid>('/api/bids', {
    method: 'POST',
    body: bidData,
    requiresAuth: true
  });
};

/**
 * Get bids for a provider
 */
export const getProviderBids = async (
  providerId: string,
  filters?: BidFilters,
  sort?: BidSortOptions,
  page = 1,
  limit = 10
): Promise<ApiResponse<BidListResponse>> => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
    ...(filters && Object.fromEntries(
      Object.entries(filters).filter(([_, value]) => value !== undefined)
    )),
    ...(sort && {
      sortField: sort.field,
      sortDirection: sort.direction
    })
  });

  return apiService<BidListResponse>(`/api/providers/${providerId}/bids?${params}`, {
    method: 'GET',
    requiresAuth: true
  });
};

/**
 * Get bids for a specific job
 */
export const getJobBids = async (
  jobId: string,
  filters?: BidFilters,
  sort?: BidSortOptions,
  page = 1,
  limit = 10
): Promise<ApiResponse<BidListResponse>> => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
    ...(filters && Object.fromEntries(
      Object.entries(filters).filter(([_, value]) => value !== undefined)
    )),
    ...(sort && {
      sortField: sort.field,
      sortDirection: sort.direction
    })
  });

  return apiService<BidListResponse>(`/api/jobs/${jobId}/bids?${params}`, {
    method: 'GET',
    requiresAuth: true
  });
};

/**
 * Get all bids (admin only)
 */
export const getAllBids = async (
  filters?: BidFilters,
  sort?: BidSortOptions,
  page = 1,
  limit = 10
): Promise<ApiResponse<BidListResponse>> => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
    ...(filters && Object.fromEntries(
      Object.entries(filters).filter(([_, value]) => value !== undefined)
    )),
    ...(sort && {
      sortField: sort.field,
      sortDirection: sort.direction
    })
  });

  return apiService<BidListResponse>(`/api/admin/bids?${params}`, {
    method: 'GET',
    requiresAuth: true
  });
};

/**
 * Get a specific bid by ID
 */
export const getBidById = async (bidId: string): Promise<ApiResponse<Bid>> => {
  return apiService<Bid>(`/api/bids/${bidId}`, {
    method: 'GET',
    requiresAuth: true
  });
};

/**
 * Update a bid
 */
export const updateBid = async (
  bidId: string,
  updateData: UpdateBidRequest
): Promise<ApiResponse<Bid>> => {
  return apiService<Bid>(`/api/bids/${bidId}`, {
    method: 'PUT',
    body: updateData,
    requiresAuth: true
  });
};

/**
 * Withdraw a bid
 */
export const withdrawBid = async (bidId: string): Promise<ApiResponse<BidActionResponse>> => {
  return apiService<BidActionResponse>(`/api/bids/${bidId}/withdraw`, {
    method: 'POST',
    requiresAuth: true
  });
};

/**
 * Accept a bid (customer only)
 */
export const acceptBid = async (bidId: string): Promise<ApiResponse<BidActionResponse>> => {
  return apiService<BidActionResponse>(`/api/bids/${bidId}/accept`, {
    method: 'POST',
    requiresAuth: true
  });
};

/**
 * Reject a bid (customer only)
 */
export const rejectBid = async (bidId: string): Promise<ApiResponse<BidActionResponse>> => {
  return apiService<BidActionResponse>(`/api/bids/${bidId}/reject`, {
    method: 'POST',
    requiresAuth: true
  });
};

/**
 * Get bid statistics (admin only)
 */
export const getBidStatistics = async (
  filters?: BidFilters
): Promise<ApiResponse<BidStatistics>> => {
  const params = new URLSearchParams(
    filters ? Object.fromEntries(
      Object.entries(filters).filter(([_, value]) => value !== undefined)
    ) : {}
  );

  return apiService<BidStatistics>(`/api/admin/bids/statistics?${params}`, {
    method: 'GET',
    requiresAuth: true
  });
};